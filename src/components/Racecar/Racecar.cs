using System;
using Godot;

public partial class Racecar : CharacterBody2D
{
    [Export]
    public Marker2D StartPosition;
    private float CarHeading = 0f;
    private float SteerAngle = 0f;

    private Vector2 Acceleration = Vector2.Zero;

    private float EnginePower = 2f;

    private float BrakePower = 5f;

    private float ReversePower = 1f;

    private bool isReversing = false;

    private float TrackFriction = -0.002f; // -0.0005
    private float OffRoadFriction = -0.01f;

    private float SlowedFriction = -0.1f;
    private float Friction = 0f;
    private float Drag = -0.00001f;

    private float SlipSpeed = 150f;
    private float TractionWhenSlipping = 0.0001f;
    private float TractionWhenGripping = 1f;

    private float StallTime = 0.5f;
    private bool isStalled = false;
    private float StallCountdown = 0f;
    private bool isCrashed = false;

    private Marker2D FrontWheel;
    private Marker2D BackWheel;

    private Sprite2D CarSprite;

    private AudioStreamPlayer2D EngineSound;
    private AudioStreamPlayer2D TireScreech;

    private AnimationPlayer AnimationPlayer;

    private Interaction2d TractionArea;

    private enum AnimateTurn
    {
        None = 0,
        Left = 1,
        Right = 2,
    }


    public override void _Ready()
    {
        FrontWheel = GetNode<Marker2D>("Front");
        BackWheel = GetNode<Marker2D>("Back");
        CarSprite = GetNode<Sprite2D>("Sprite2D");
        EngineSound = GetNode<AudioStreamPlayer2D>("EngineSound");
        TireScreech = GetNode<AudioStreamPlayer2D>("TireScreech");
        AnimationPlayer = GetNode<AnimationPlayer>("AnimationPlayer");
        TractionArea = GetNode<Interaction2d>("TractionArea");

        Reset();
    }

    public override void _PhysicsProcess(double delta)
    {
        if (StallCountdown > 0)
        {
            StallCountdown -= (float)delta;
            if (StallCountdown <= 0)
            {
                isStalled = false;
                if (isCrashed)
                {
                    Reset();
                }
            }

            if (isStalled)
            {
                MoveAndSlide();
                return;
            }
        }

        int turn = 0;
        if (Input.IsActionPressed("steer_right"))
        {
            turn = 1;
            CarSprite.SetFrame((int)AnimateTurn.Right);
        }
        if (Input.IsActionPressed("steer_left"))
        {
            turn = -1;
            CarSprite.SetFrame((int)AnimateTurn.Left);
        }

        if (turn == 0)
        {
            CarSprite.SetFrame((int)AnimateTurn.None);
        }

        if (Input.IsActionPressed("brake-reverse"))
        {
            EngineSound.Stop();
            if (isReversing)
            {
                Acceleration = -1 * Transform.X * ReversePower;
            }
            else
            {
                Acceleration = -1 * Transform.X * BrakePower;
            }
        }
        else
        {
            Acceleration = Transform.X * EnginePower;
            if (!EngineSound.IsPlaying())
            {
                EngineSound.Play();
            }
        }

        if (TractionArea.Interactions().Length > 0)
        {
            Friction = TrackFriction;
            foreach (Interaction2d interaction in TractionArea.Interactions())
            {
                if (interaction.Name == "SlowArea")
                {
                    Friction = SlowedFriction;
                    break;
                }
            }
        }
        else
        {
            Friction = OffRoadFriction;
        }

        Vector2 frictionForce = Velocity * Friction;
        Vector2 dragForce = Velocity * Velocity.Length() * Drag;

        Acceleration += frictionForce + dragForce;


        float traction = TractionWhenGripping;
        float turnRadians = turn * Mathf.DegToRad(0.2f);
        if (Velocity.Length() < SlipSpeed)
        {
            turnRadians = turn * Mathf.DegToRad(0.75f);
        }
        if (turn != 0 && Input.IsActionPressed("drift"))
        {
            traction = TractionWhenSlipping;
            turnRadians = turn * Mathf.DegToRad(0.5f);
            Acceleration = Vector2.Zero;
            if (!TireScreech.IsPlaying())
            {
                TireScreech.Play();
            }
        }
        SteerAngle = turnRadians;

        Vector2 backWheel = BackWheel.GlobalPosition + Velocity;
        Vector2 frontWheel = FrontWheel.GlobalPosition + Velocity.Rotated(SteerAngle);
        Vector2 newHeading = backWheel.DirectionTo(frontWheel);

        float newHeadingDot = newHeading.Dot(Velocity.Normalized());
        if (newHeadingDot > 0)
        {
            Velocity = Velocity.Lerp(newHeading * Velocity.Length(), traction);
            isReversing = false;
        }
        else
        {
            Velocity = Velocity.Lerp(-1 * newHeading * Velocity.Length(), traction);
            isReversing = true;
        }

        Velocity += Acceleration;

        Rotation = newHeading.Angle();

        KinematicCollision2D collision = MoveAndCollide(Velocity * (float)delta);

        if (collision != null)
        {
            GodotObject collider = collision.GetCollider();
            if (collider is StaticBody2D staticBody)
            {
                isStalled = true;
                StallCountdown = StallTime;
                if (staticBody.Name == "CrashObstacles")
                {
                    GD.Print("Crash!");
                    isCrashed = true;
                }
                else if (staticBody.Name == "BounceObstacles")
                {
                    Velocity = collision.GetNormal() * 50f;
                }
            }
        }
        if (Velocity.Length() < 10)
        {
            AnimationPlayer.Play("idle");
        } else if (Velocity.Length() < SlipSpeed / 2)
        {
            AnimationPlayer.Play("slow");
        } else if (Velocity.Length() < SlipSpeed)
        {
            AnimationPlayer.Play("medium");
        } else
        {
            AnimationPlayer.Play("fast");
        }
    }

    private void Reset()
    {
        GlobalPosition = StartPosition.GlobalPosition;
        Rotation = StartPosition.Rotation;
        Velocity = Vector2.Zero;
        Acceleration = Vector2.Zero;
        SteerAngle = 0;
        isCrashed = false;
        isStalled = false;
        StallCountdown = 0;
    }
}
