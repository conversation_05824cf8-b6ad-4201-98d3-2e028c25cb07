using Godot;

public partial class MiniMap : SubViewport
{
    [Export]
    public Racecar Racecar;
    private Camera2D MiniMapCamera;

    public override void _Ready()
    {
        MiniMapCamera = GetNode<Camera2D>("Camera2D");
        World2D = GetTree().Root.World2D;
    }

    public override void _Process(double delta)
    {
        MiniMapCamera.GlobalPosition = Racecar.GlobalPosition;
    }
}
