using Godot;
using System;

public partial class LapTimer : Label
{
    [Export]
    public Interaction2d StartLapInteraction;

    [Export]
    public Racecar Racer;
    private int LapCount = 0;
    private float LapTime = 0f;
    private float FastestLapTime = float.PositiveInfinity;


    Array PreviousInteractions;

    public override void _Process(double delta)
    {
        if (StartLapInteraction.Interactions().Length > 0 && PreviousInteractions.Length == 0)
        {
            if (LapCount >= 1)
            {
                FastestLapTime = Math.Min(FastestLapTime, LapTime);
            }
            LapCount++;
            LapTime = 0f;
        }

        if (LapCount > 1)
        {
            Text = "Speed: " + Racer.Velocity.Length().ToString("0.00") + "\nLap: " + LapCount.ToString("0") + "\nTime: " + LapTime.ToString("0.00") + "\nFastest Lap: " + FastestLapTime.ToString("0.00");
        }
        else if (LapCount == 1)
        {
            Text = "Speed: " + Racer.Velocity.Length().ToString("0.00") + "\nLap: " + LapCount.ToString("0") + "\nTime: " + LapTime.ToString("0.00") + "\n";
        }

        LapTime += (float)delta;
        PreviousInteractions = StartLapInteraction.Interactions();
    }
}
