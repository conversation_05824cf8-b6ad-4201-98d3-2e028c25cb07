using Godot;
using System;
using System.Collections.Generic;
using System.Linq;

public partial class Interaction2d : Area2D
{
    [Signal]
    public delegate void InteractionAddedEventHandler(Interaction2d interaction);

    [Signal]
    public delegate void InteractionLostEventHandler(Interaction2d interaction);

    private readonly List<Interaction2d> _interactions = new();

    public override void _Ready()
    {
        AreaEntered += OnAreaEntered;
        AreaExited += OnAreaExited;
    }

    public Array Interactions()
    {
        return _interactions.ToArray();
    }

    public void EnterInteraction(Interaction2d with)
    {
        _interactions.Add(with);
        EmitSignal(SignalName.InteractionAdded, with);
    }

    public void LeaveInteraction(Interaction2d with)
    {
        int i = with._interactions.IndexOf(this);
        if (i >= 0 && i < with._interactions.Count)
        {
            with._interactions.RemoveAt(i);
            with.EmitSignal(SignalName.InteractionLost, this);
        }

        i = _interactions.IndexOf(with);
        if (i >= 0 && i < _interactions.Count)
        {
            _interactions.RemoveAt(i);
            EmitSignal(SignalName.InteractionLost, with);
        }
    }

    public void Poked(Interaction2d from)
    {
        if (!Monitoring)
            return;

        if (!_interactions.Contains(from) && GetOverlappingAreas().Contains(from))
        {
            EnterInteraction(from);
            from.EnterInteraction(this);
        }
    }

    public void Monitor(bool on = true)
    {
        if (on)
        {
            Monitoring = true;
            foreach (Area2D area in GetOverlappingAreas())
            {
                if (area is Interaction2d interaction && !_interactions.Contains(interaction))
                {
                    OnAreaEntered(area);
                }
            }
        }
        else
        {
            Monitoring = false;
            var interactionsToRemove = _interactions.ToList();
            foreach (Interaction2d interaction in interactionsToRemove)
            {
                LeaveInteraction(interaction);
            }
        }
    }

    private void OnAreaEntered(Area2D area)
    {
        if (area.HasMethod("Poked"))
        {
            area.Call("Poked", this);
        }
    }

    private void OnAreaExited(Area2D area)
    {
        if (area.HasMethod("LeaveInteraction"))
        {
            area.Call("LeaveInteraction", this);
        }
    }
}
